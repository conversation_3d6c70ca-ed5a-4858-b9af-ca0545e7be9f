<?php

require_once 'vendor/autoload.php';

try {
    $app = require_once 'bootstrap/app.php';
    echo "Application created successfully\n";

    // Check if cache is bound
    if ($app->bound('cache')) {
        echo "Cache service is bound\n";
    } else {
        echo "Cache service is NOT bound\n";
    }

    // Check what services are bound
    echo "Checking cache-related bindings:\n";
    $bindings = $app->getBindings();
    foreach ($bindings as $key => $binding) {
        if (strpos($key, 'cache') !== false) {
            echo "Found binding: $key\n";
        }
    }

    // Try to resolve cache service
    $cache = $app->make('cache');
    echo "Cache service resolved successfully\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

<?php

use Illuminate\Support\Facades\Broadcast;
use Modules\ChatBot\Models\Conversation;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

// ChatBot conversation channels
Broadcast::channel('conversation.{conversationId}', function ($user, $conversationId) {
    // Check if user has access to this conversation
    $conversation = Conversation::find($conversationId);
    
    if (!$conversation) {
        return false;
    }
    
    // User can access if they own the conversation or have access to the bot
    return $conversation->user_id === $user->id || 
           $conversation->bot->canBeAccessedBy($user->id);
});

// ChatBot bot channels (for bot-wide notifications)
Broadcast::channel('bot.{botId}', function ($user, $botId) {
    $bot = \Modules\ChatBot\Models\Bot::find($botId);
    
    if (!$bot) {
        return false;
    }
    
    return $bot->canBeAccessedBy($user->id);
});

// ChatBot user channels (for user-specific notifications)
Broadcast::channel('chatbot.user.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

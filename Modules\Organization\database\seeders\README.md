# Organization Module Seeders

Thư mục này chứa các seeder cho Organization module, bao gồm dữ liệu mẫu cho organizations, members, và invitations.

## Các Seeder Có Sẵn

### 1. OrganizationPermissionSeeder
- Tạ<PERSON> các permissions cần thiết cho Organization module
- Chạy tự động khi seed database

### 2. OrganizationSeeder
- Tạo dữ liệu mẫu cơ bản với cấu trúc có ý nghĩa
- <PERSON><PERSON> gồm:
  - 6 users mẫu (admin + 5 users khác)
  - 2 company chính (ACME Corporation, Tech Startup Inc.)
  - 4 teams con (Engineering, Marketing, Sales, Development)
  - 14 organization members với các roles khác nhau
  - 6 invitations (4 pending, 2 expired)

### 3. OrganizationFactorySeeder
- Tạo dữ liệu mẫu ngẫu nhiên sử dụng factories
- Phù hợp cho testing và development
- Tạo nhiều dữ liệu hơn với cấu trúc đa dạng

## Cách Sử Dụng

### Chạy tất cả seeders của Organization module:
```bash
php artisan db:seed --class="Modules\Organization\Database\Seeders\OrganizationDatabaseSeeder"
```

### Chạy từng seeder riêng lẻ:

#### Chạy seeder dữ liệu cơ bản:
```bash
php artisan db:seed --class="Modules\Organization\Database\Seeders\OrganizationSeeder"
```

#### Chạy seeder factory (cho testing):
```bash
php artisan db:seed --class="Modules\Organization\Database\Seeders\OrganizationFactorySeeder"
```

#### Chạy seeder permissions:
```bash
php artisan db:seed --class="Modules\Organization\Database\Seeders\OrganizationPermissionSeeder"
```

## Dữ Liệu Mẫu Được Tạo

### Users
- `<EMAIL>` - System Admin (owner của ACME Corporation)
- `<EMAIL>` - John Doe (owner của Engineering Team)
- `<EMAIL>` - Jane Smith (owner của Tech Startup Inc.)
- `<EMAIL>` - Mike Johnson (owner của Development Team)
- `<EMAIL>` - Sarah Wilson (owner của Marketing Team)
- `<EMAIL>` - David Brown (owner của Sales Team)

### Organizations

#### ACME Corporation (Company)
- **Type**: Company
- **Owner**: System Admin
- **Visibility**: Public
- **Teams**:
  - Engineering Team (John Doe)
  - Marketing Team (Sarah Wilson)
  - Sales Team (David Brown)

#### Tech Startup Inc. (Company)
- **Type**: Company
- **Owner**: Jane Smith
- **Visibility**: Private
- **Teams**:
  - Development Team (Mike Johnson)

### Organization Members
Mỗi organization có các members với roles khác nhau:
- **admin**: Có thể quản lý members và organization
- **editor**: Có thể chỉnh sửa nội dung
- **viewer**: Chỉ có thể xem
- **member**: Member thông thường
- **guest**: Quyền hạn chế

### Invitations
- 4 pending invitations cho các email mới
- 2 expired invitations để test chức năng

## Lưu Ý

1. **Conflict Resolution**: Seeder sử dụng `firstOrCreate()` để tránh duplicate data
2. **Dependencies**: Cần có User module đã được seed trước
3. **Testing**: Sử dụng OrganizationFactorySeeder cho môi trường test
4. **Production**: Chỉ chạy OrganizationSeeder cho dữ liệu cơ bản

## Cấu Trúc Dữ Liệu

### Organization Fields
- `uuid`: UUID duy nhất
- `parent_id`: ID của organization cha (cho hierarchy)
- `type`: 'company' hoặc 'team'
- `owner_id`: ID của user sở hữu
- `name`: Tên organization
- `slug`: URL-friendly identifier
- `description`: Mô tả
- `visibility`: 'private', 'internal', 'public'
- `status`: 'active', 'inactive'
- `settings`: JSON settings

### Member Roles
- `admin`: Quản lý toàn bộ
- `editor`: Chỉnh sửa nội dung
- `viewer`: Chỉ xem
- `member`: Thành viên thông thường
- `guest`: Quyền hạn chế

### Invitation Status
- `pending`: Đang chờ chấp nhận
- `expired`: Đã hết hạn

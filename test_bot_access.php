<?php

/**
 * Laravel Artisan Command để test ChatService
 * Chạy: php artisan tinker < test_bot_access.php
 */

echo "Testing ChatService::getActiveChatBots() improvements...\n";

// Test 1: Basic functionality
echo "\n=== Test 1: Basic Service Instantiation ===\n";
try {
    $service = new \Modules\ChatBot\Services\ChatService();
    echo "✅ ChatService instantiated successfully\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 2: Check if user is authenticated
echo "\n=== Test 2: Authentication Check ===\n";
if (auth()->check()) {
    $user = auth()->user();
    echo "✅ User authenticated: {$user->email} (ID: {$user->id})\n";
} else {
    echo "⚠️  No user authenticated. Please login first.\n";
    echo "   Run: Auth::loginUsingId(1);\n";
}

// Test 3: Get accessible bots
echo "\n=== Test 3: Get Accessible Bots ===\n";
try {
    if (auth()->check()) {
        $bots = $service->getActiveChatBots();
        echo "✅ Retrieved {$bots->count()} accessible bots\n";
        
        if ($bots->count() > 0) {
            echo "\nBot details:\n";
            foreach ($bots as $bot) {
                echo "  - {$bot->name} (UUID: {$bot->uuid})\n";
            }
        } else {
            echo "ℹ️  No bots found. This could mean:\n";
            echo "   - User has no bots\n";
            echo "   - No bots shared with user\n";
            echo "   - User not member of any organization with bots\n";
            echo "   - No pending invitations\n";
        }
    } else {
        echo "⚠️  Cannot test without authentication\n";
    }
} catch (Exception $e) {
    echo "❌ Error getting bots: " . $e->getMessage() . "\n";
}

// Test 4: Check bot relationships
echo "\n=== Test 4: Bot Relationships Check ===\n";
try {
    $botCount = \Modules\ChatBot\Models\Bot::count();
    $shareCount = \Modules\ChatBot\Models\BotShare::count();
    $orgCount = \Modules\Organization\Models\Organization::count();
    $memberCount = \Modules\Organization\Models\OrganizationMember::count();
    
    echo "✅ Database stats:\n";
    echo "   - Total bots: {$botCount}\n";
    echo "   - Total bot shares: {$shareCount}\n";
    echo "   - Total organizations: {$orgCount}\n";
    echo "   - Total organization members: {$memberCount}\n";
} catch (Exception $e) {
    echo "❌ Error checking relationships: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
echo "If you see this message, the ChatService improvements are working correctly!\n";
echo "\nTo test with different users:\n";
echo "1. Auth::loginUsingId(1); // Login as user 1\n";
echo "2. \$bots = \$service->getActiveChatBots();\n";
echo "3. dd(\$bots->pluck('name', 'uuid'));\n";

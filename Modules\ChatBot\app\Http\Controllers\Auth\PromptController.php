<?php

namespace Modules\ChatBot\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Exception;
use Illuminate\Http\Request;
use Modules\ChatBot\Facades\AIFacade;
use Modules\Core\Traits\ResponseTrait;

class PromptController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {

    }

    /**
     * @throws Exception
     */
    public function getBotGeneralPrompt(Request $request)
    {
       $text = AIFacade::botGeneralPrompts();
       if (is_null($text)) {
           $this->errorResponse([], __('Bad request!'));
       }
       return $this->successResponse($text);
    }


}

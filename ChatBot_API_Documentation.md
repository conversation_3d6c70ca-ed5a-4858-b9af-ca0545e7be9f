# ChatBot API Documentation

## Tổng quan
Tài liệu này mô tả các API endpoints liên quan đến chức năng chat và bot trong hệ thống Laravel ProCMS.

## Base URL
```
/api/v1
```

## Authentication
Hầu hết các API endpoints yêu cầu authentication thông qua Bearer token:
```
Authorization: Bearer {your-api-token}
```

---

## 1. Bot Management APIs

### 1.1 L<PERSON>y danh sách bots công khai
**Endpoint:** `GET /api/v1/bots/public`  
**Authentication:** Không yêu cầu  
**Mô tả:** L<PERSON>y danh sách các bot công khai có thể truy cập

**Response:**
```json
{
  "success": true,
  "message": "Public bots retrieved successfully",
  "data": [
    {
      "uuid": "bot-uuid-here",
      "name": "Bot Name",
      "description": "Bot description",
      "status": "active",
      "visibility": "public",
      "bot_type": "personal",
      "is_shareable": true,
      "created_at": "2024-01-01T00:00:00.000000Z",
      "updated_at": "2024-01-01T00:00:00.000000Z"
    }
  ]
}
```

### 1.2 Lấy danh sách bots của user
**Endpoint:** `GET /api/v1/auth/bots`  
**Authentication:** Required  
**Mô tả:** Lấy danh sách các bot mà user sở hữu

**Response:**
```json
{
  "success": true,
  "message": "Bots retrieved successfully",
  "data": [
    {
      "uuid": "bot-uuid-here",
      "name": "My Bot",
      "description": "My personal bot",
      "status": "active",
      "visibility": "private",
      "bot_type": "personal",
      "is_shareable": true,
      "aiModel": {
        "key": "gpt-4",
        "name": "GPT-4"
      },
      "owner": {
        "first_name": "John",
        "last_name": "Doe",
        "full_name": "John Doe",
        "avatar": "avatar-url"
      },
      "created_at": "2024-01-01T00:00:00.000000Z",
      "updated_at": "2024-01-01T00:00:00.000000Z"
    }
  ]
}
```

### 1.3 Tạo bot mới
**Endpoint:** `POST /api/v1/auth/bots`  
**Authentication:** Required  
**Mô tả:** Tạo một bot mới

**Request Body:**
```json
{
  "name": "Bot Name",
  "description": "Bot description",
  "model": "gpt-4",
  "system_prompt": "You are a helpful assistant",
  "parameters": {
    "temperature": 0.7,
    "max_tokens": 2048,
    "top_p": 1.0,
    "frequency_penalty": 0,
    "presence_penalty": 0
  },
  "tool_calling_mode": "auto",
  "status": "active",
  "visibility": "private",
  "bot_type": "personal",
  "is_shareable": true,
  "metadata": {}
}
```

**Validation Rules:**
- `name`: required, string, max:120
- `description`: nullable, string, max:65535
- `model`: required, string, exists:model_ai,key
- `system_prompt`: required, string, max:65535
- `parameters.temperature`: nullable, numeric, min:0, max:2
- `parameters.max_tokens`: nullable, integer, min:1, max:100000
- `parameters.top_p`: nullable, numeric, min:0, max:1

**Response (201):**
```json
{
  "success": true,
  "message": "Bot created successfully",
  "data": {
    "uuid": "new-bot-uuid",
    "name": "Bot Name",
    "description": "Bot description",
    "status": "active",
    "visibility": "private",
    "bot_type": "personal",
    "is_shareable": true,
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}
```

### 1.4 Lấy thông tin chi tiết bot
**Endpoint:** `GET /api/v1/auth/bots/{uuid}`  
**Authentication:** Required  
**Mô tả:** Lấy thông tin chi tiết của một bot

**Response:**
```json
{
  "success": true,
  "message": "Bot retrieved successfully",
  "data": {
    "uuid": "bot-uuid-here",
    "name": "Bot Name",
    "description": "Bot description",
    "system_prompt": "You are a helpful assistant",
    "parameters": {
      "temperature": 0.7,
      "max_tokens": 2048
    },
    "status": "active",
    "visibility": "private",
    "bot_type": "personal",
    "is_shareable": true,
    "aiModel": {
      "key": "gpt-4",
      "name": "GPT-4"
    },
    "owner": {
      "first_name": "John",
      "last_name": "Doe",
      "full_name": "John Doe"
    },
    "user_permission": "admin",
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}
```

### 1.5 Cập nhật bot
**Endpoint:** `POST /api/v1/auth/bots/{uuid}`  
**Authentication:** Required  
**Mô tả:** Cập nhật thông tin bot

**Request Body:** (Tương tự như tạo bot, nhưng tất cả fields đều optional)

**Response:**
```json
{
  "success": true,
  "message": "Bot updated successfully",
  "data": {
    // Bot data after update
  }
}
```

### 1.6 Tìm kiếm bots
**Endpoint:** `GET /api/v1/auth/bots/search`  
**Authentication:** Required  
**Mô tả:** Tìm kiếm bots theo từ khóa

**Query Parameters:**
- `query`: required, string, max:255
- `include_public`: nullable, boolean (default: true)
- `per_page`: nullable, integer, min:1, max:100 (default: 15)

**Response:**
```json
{
  "success": true,
  "message": "Search results retrieved successfully",
  "data": {
    "data": [
      // Array of bot objects
    ],
    "meta": {
      "current_page": 1,
      "per_page": 15,
      "total": 25,
      "last_page": 2
    }
  }
}
```

---

## 2. Conversation Management APIs

### 2.1 Lấy danh sách conversations
**Endpoint:** `GET /api/v1/auth/conversations`  
**Authentication:** Required  
**Mô tả:** Lấy danh sách conversations của user

**Query Parameters:**
- `bot_uuid`: nullable, string, exists:bots,uuid
- `status`: nullable, string, in:active,completed,archived
- `search`: nullable, string, max:255
- `per_page`: nullable, integer, min:1, max:100 (default: 15)

**Response:**
```json
{
  "success": true,
  "message": "Conversations retrieved successfully",
  "data": [
    {
      "id": 1,
      "title": "Conversation Title",
      "bot_id": 1,
      "user_id": 1,
      "user_type": "App\\Models\\User",
      "status": "active",
      "message_count": 5,
      "created_at": "2024-01-01T00:00:00.000000Z",
      "bot": {
        "uuid": "bot-uuid",
        "name": "Bot Name"
      }
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 15,
    "total": 10
  }
}
```

### 2.2 Tạo conversation mới
**Endpoint:** `POST /api/v1/auth/conversations`  
**Authentication:** Required  
**Mô tả:** Tạo conversation mới

**Request Body:**
```json
{
  "title": "New Conversation",
  "bot_uuid": "bot-uuid-here"
}
```

**Response (201):**
```json
{
  "success": true,
  "message": "Conversation created successfully",
  "data": {
    "id": 1,
    "title": "New Conversation",
    "bot_id": 1,
    "user_id": 1,
    "user_type": "App\\Models\\User",
    "status": "active",
    "bot": {
      "uuid": "bot-uuid",
      "name": "Bot Name"
    },
    "user": {
      "id": 1,
      "name": "User Name"
    }
  }
}
```

### 3.2 Tạo message mới
**Endpoint:** `POST /api/v1/auth/messages`
**Authentication:** Required
**Mô tả:** Tạo message mới trong conversation

**Request Body:**
```json
{
  "conversation_id": 1,
  "content": "Hello, I need help with my account",
  "content_type": "text"
}
```

**Response (201):**
```json
{
  "success": true,
  "message": "Message created successfully",
  "data": {
    "id": 1,
    "conversation_id": 1,
    "role": "user",
    "content": "Hello, I need help with my account",
    "content_type": "text",
    "status": "completed",
    "created_at": "2024-01-01T00:00:00.000000Z"
  }
}
```

### 3.3 Lấy thông tin message
**Endpoint:** `GET /api/v1/auth/messages/{messageId}`
**Authentication:** Required
**Mô tả:** Lấy thông tin chi tiết của một message

**Response:**
```json
{
  "success": true,
  "message": "Message retrieved successfully",
  "data": {
    "id": 1,
    "conversation_id": 1,
    "role": "user",
    "content": "Hello, I need help",
    "content_type": "text",
    "status": "completed",
    "created_at": "2024-01-01T00:00:00.000000Z",
    "conversation": {
      "id": 1,
      "title": "Support Chat"
    }
  }
}
```

### 3.4 Tạo AI response cho conversation
**Endpoint:** `POST /api/v1/auth/conversations/{conversationUuid}/generate-response`
**Authentication:** Required
**Mô tả:** Tạo AI response cho conversation hiện tại

**Request Body:**
```json
{
  "temperature": 0.7,
  "max_tokens": 2048,
  "stream": false
}
```

**Response (201):**
```json
{
  "success": true,
  "message": "AI response generated successfully",
  "data": {
    "id": 2,
    "role": "assistant",
    "content": "I'd be happy to help you with your account. What specific issue are you experiencing?",
    "model_used": "gpt-4",
    "status": "completed",
    "prompt_tokens": 15,
    "completion_tokens": 20,
    "total_tokens": 35,
    "cost": 0.0002,
    "created_at": "2024-01-01T00:00:00.000000Z"
  }
}
```

### 3.5 Gửi message và nhận AI response
**Endpoint:** `POST /api/v1/auth/messages/send-and-respond`
**Authentication:** Required
**Mô tả:** Gửi message và nhận AI response trong một request

**Request Body:**
```json
{
  "conversation_id": 1,
  "content": "What is the weather like today?",
  "temperature": 0.8,
  "max_tokens": 1024,
  "stream": false
}
```

**Response (201):**
```json
{
  "success": true,
  "message": "Message sent and AI response generated successfully",
  "data": {
    "user_message": {
      "id": 1,
      "role": "user",
      "content": "What is the weather like today?"
    },
    "ai_message": {
      "id": 2,
      "role": "assistant",
      "content": "I don't have access to real-time weather data. To get current weather information, I'd recommend checking a weather app or website like Weather.com or your local weather service.",
      "model_used": "gpt-4"
    }
  }
}
```

### 3.6 Streaming AI response
**Endpoint:** `POST /api/v1/auth/conversations/{conversationUuid}/stream-response`
**Authentication:** Required
**Mô tả:** Tạo AI response với streaming (Server-Sent Events)

**Request Body:**
```json
{
  "temperature": 0.7,
  "max_tokens": 2048
}
```

**Response:** Server-Sent Events stream
```
data: {"type": "start", "message_id": 123}

data: {"type": "content", "content": "Hello"}

data: {"type": "content", "content": " there!"}

data: {"type": "end", "message_id": 123, "total_tokens": 15}
```

---

## 4. Knowledge Base APIs

### 4.1 Lấy danh sách knowledge bases
**Endpoint:** `GET /api/v1/auth/knowledge-bases`
**Authentication:** Required
**Mô tả:** Lấy danh sách knowledge bases của user

**Query Parameters:**
- `per_page`: nullable, integer, min:1, max:100 (default: 15)
- `status`: nullable, string, in:pending,processing,ready,error
- `type`: nullable, string, in:file,text
- `search`: nullable, string, max:255

**Response:**
```json
{
  "success": true,
  "message": "Knowledge bases retrieved successfully",
  "data": [
    {
      "uuid": "kb-uuid-here",
      "name": "My Knowledge Base",
      "type": "text",
      "status": "ready",
      "content_length": 1500,
      "created_at": "2024-01-01T00:00:00.000000Z"
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 15,
    "total": 5
  }
}
```

### 4.2 Tạo knowledge base từ text
**Endpoint:** `POST /api/v1/auth/knowledge-bases/text`
**Authentication:** Required
**Mô tả:** Tạo knowledge base từ text content

**Request Body:**
```json
{
  "name": "Knowledge Base Name",
  "content": "This is the knowledge base content..."
}
```

**Response (201):**
```json
{
  "success": true,
  "message": "Knowledge base created successfully",
  "data": {
    "uuid": "new-kb-uuid",
    "name": "Knowledge Base Name",
    "type": "text",
    "status": "ready",
    "content_length": 45,
    "created_at": "2024-01-01T00:00:00.000000Z"
  }
}
```

### 4.3 Tạo knowledge base từ file
**Endpoint:** `POST /api/v1/auth/knowledge-bases/file`
**Authentication:** Required
**Mô tả:** Tạo knowledge base từ file upload

**Request Body:** (multipart/form-data)
- `name`: string, required
- `file`: file, required (pdf, txt, docx, etc.)

**Response (201):**
```json
{
  "success": true,
  "message": "Knowledge base created successfully from file",
  "data": {
    "uuid": "new-kb-uuid",
    "name": "Document Knowledge Base",
    "type": "file",
    "status": "processing",
    "file_name": "document.pdf",
    "file_size": 1024000,
    "created_at": "2024-01-01T00:00:00.000000Z"
  }
}
```

### 4.4 Lấy thông tin knowledge base
**Endpoint:** `GET /api/v1/auth/knowledge-bases/{uuid}`
**Authentication:** Required
**Mô tả:** Lấy thông tin chi tiết knowledge base

**Response:**
```json
{
  "success": true,
  "message": "Knowledge base retrieved successfully",
  "data": {
    "uuid": "kb-uuid-here",
    "name": "My Knowledge Base",
    "type": "text",
    "status": "ready",
    "content": "Knowledge base content...",
    "content_length": 1500,
    "metadata": {
      "content_length": 1500,
      "updated_at": "2024-01-01T00:00:00.000000Z"
    },
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}
```

### 4.5 Cập nhật knowledge base
**Endpoint:** `PUT /api/v1/auth/knowledge-bases/{uuid}`
**Authentication:** Required
**Mô tả:** Cập nhật knowledge base (chỉ cho text-based)

**Request Body:**
```json
{
  "name": "Updated Knowledge Base Name",
  "content": "Updated content..."
}
```

**Response:**
```json
{
  "success": true,
  "message": "Knowledge base updated successfully",
  "data": {
    // Updated knowledge base data
  }
}
```

### 4.6 Xóa knowledge base
**Endpoint:** `DELETE /api/v1/auth/knowledge-bases/{uuid}`
**Authentication:** Required
**Mô tả:** Xóa knowledge base

**Response:**
```json
{
  "success": true,
  "message": "Knowledge base deleted successfully"
}
```

---

## 5. Prompt Management APIs

### 5.1 Lấy general prompt cho bot
**Endpoint:** `GET /api/v1/auth/bot-general-prompt`
**Authentication:** Required
**Mô tả:** Lấy general prompt template cho bot

**Response:**
```json
{
  "success": true,
  "message": "General prompt retrieved successfully",
  "data": {
    "prompt": "You are a helpful AI assistant. Please respond to user queries in a helpful and informative manner."
  }
}
```

---

## 6. Error Responses

### 6.1 Validation Errors (422)
```json
{
  "success": false,
  "message": "The given data was invalid.",
  "errors": {
    "name": ["The name field is required."],
    "model": ["The selected model is invalid."]
  }
}
```

### 6.2 Authentication Error (401)
```json
{
  "success": false,
  "message": "Unauthenticated."
}
```

### 6.3 Authorization Error (403)
```json
{
  "success": false,
  "message": "This action is unauthorized."
}
```

### 6.4 Not Found Error (404)
```json
{
  "success": false,
  "message": "Resource not found."
}
```

### 6.5 Server Error (500)
```json
{
  "success": false,
  "message": "Internal server error occurred."
}
```

---

## 7. Rate Limiting

Tất cả API endpoints đều có rate limiting:
- **Authenticated users**: 60 requests per minute
- **Unauthenticated users**: 10 requests per minute

Rate limit headers được trả về trong response:
```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1640995200
```

---

## 8. Pagination

Các API trả về danh sách đều hỗ trợ pagination với format sau:

**Query Parameters:**
- `page`: số trang (default: 1)
- `per_page`: số items per page (default: 15, max: 100)

**Response Meta:**
```json
{
  "meta": {
    "current_page": 1,
    "per_page": 15,
    "total": 100,
    "last_page": 7,
    "from": 1,
    "to": 15
  }
}
```

---

## 9. Content Types

API hỗ trợ các content types sau:
- `application/json` (default)
- `multipart/form-data` (cho file uploads)

Response luôn là `application/json`.

---

## 10. Status Codes

- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Validation Error
- `429`: Too Many Requests
- `500`: Internal Server Error
```

### 2.3 Lấy chi tiết conversation
**Endpoint:** `GET /api/v1/auth/conversations/{uuid}`  
**Authentication:** Required  
**Mô tả:** Lấy thông tin chi tiết conversation và messages

**Response:**
```json
{
  "success": true,
  "message": "Conversation retrieved successfully",
  "data": {
    "id": 1,
    "title": "Conversation Title",
    "status": "active",
    "bot": {
      "uuid": "bot-uuid",
      "name": "Bot Name"
    },
    "user": {
      "id": 1,
      "name": "User Name"
    },
    "messages": [
      {
        "id": 1,
        "role": "user",
        "content": "Hello",
        "created_at": "2024-01-01T00:00:00.000000Z"
      },
      {
        "id": 2,
        "role": "assistant",
        "content": "Hi there!",
        "created_at": "2024-01-01T00:00:00.000000Z"
      }
    ]
  }
}
```

### 2.4 Tìm kiếm conversations
**Endpoint:** `GET /api/v1/auth/conversations/search`  
**Authentication:** Required  
**Mô tả:** Tìm kiếm conversations theo từ khóa

**Query Parameters:**
- `query`: required, string, max:255
- `per_page`: nullable, integer, min:1, max:100 (default: 15)

**Response:**
```json
{
  "success": true,
  "message": "Search results retrieved successfully",
  "data": [
    // Array of conversation objects
  ],
  "meta": {
    "current_page": 1,
    "per_page": 15,
    "total": 5
  }
}
```

---

## 3. Message Management APIs

### 3.1 Lấy danh sách messages trong conversation
**Endpoint:** `GET /api/v1/auth/conversations/{conversationUuid}/messages`  
**Authentication:** Required  
**Mô tả:** Lấy danh sách messages trong một conversation

**Query Parameters:**
- `role`: nullable, string, in:user,assistant,system,tool
- `status`: nullable, string, in:pending,streaming,completed,failed,cancelled
- `content_type`: nullable, string, in:text,image,file,audio,video,mixed
- `per_page`: nullable, integer, min:1, max:100 (default: 50)

**Response:**
```json
{
  "success": true,
  "message": "Messages retrieved successfully",
  "data": [
    {
      "id": 1,
      "role": "user",
      "content": "Hello, how are you?",
      "content_type": "text",
      "status": "completed",
      "created_at": "2024-01-01T00:00:00.000000Z"
    },
    {
      "id": 2,
      "role": "assistant",
      "content": "I'm doing well, thank you!",
      "content_type": "text",
      "status": "completed",
      "model_used": "gpt-4",
      "prompt_tokens": 10,
      "completion_tokens": 8,
      "total_tokens": 18,
      "cost": 0.0001,
      "created_at": "2024-01-01T00:00:00.000000Z"
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 50,
    "total": 2
  }
}
```

<?php

namespace Modules\ChatBot\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Modules\ChatBot\Jobs\ProcessChatMessage;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Enums\MessageStatus;
use Modules\ModelAI\Models\ModelAI;
use Modules\ModelAI\Models\ModelProvider;
use Modules\User\Models\User;

class ProcessChatMessageTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Bot $bot;
    private Conversation $conversation;
    private ModelProvider $provider;
    private ModelAI $aiModel;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setupTestData();
    }

    protected function setupTestData(): void
    {
        // Create test user
        $this->user = User::factory()->create();

        // Create provider
        $this->provider = ModelProvider::factory()->create([
            'name' => 'Google',
            'key' => 'google',
            'api_key' => 'test-api-key',
            'status' => 'active',
        ]);

        // Create AI model
        $this->aiModel = ModelAI::factory()->create([
            'key' => 'gemini-pro',
            'name' => 'Gemini Pro',
            'model_provider_id' => $this->provider->id,
            'status' => 'active',
        ]);

        // Create test bot
        $this->bot = Bot::factory()->create([
            'name' => 'Test Bot',
            'model_ai_id' => $this->aiModel->id,
            'owner_id' => $this->user->id,
            'owner_type' => get_class($this->user),
            'system_prompt' => 'You are a helpful assistant. Always be polite and informative.',
            'status' => 'active',
        ]);

        // Create conversation
        $this->conversation = Conversation::factory()->create([
            'bot_id' => $this->bot->id,
            'owner_id' => $this->user->id,
            'owner_type' => get_class($this->user),
            'status' => 'active',
        ]);
    }

    /** @test */
    public function it_can_prepare_api_contents_with_system_prompt_first()
    {
        // Create a user message to trigger the job
        $userMessage = Message::factory()->create([
            'conversation_id' => $this->conversation->id,
            'role' => MessageRole::USER,
            'content' => 'Hello, how are you?',
            'status' => MessageStatus::COMPLETED,
        ]);

        // Create the job
        $job = new ProcessChatMessage([$this->bot, $userMessage]);

        // Use reflection to access protected method
        $reflection = new \ReflectionClass($job);
        $method = $reflection->getMethod('prepareApiContents');
        $method->setAccessible(true);

        // Test with empty conversation history
        $emptyHistory = collect([]);
        $result = $method->invoke($job, $emptyHistory);

        // Should have system prompt as first message
        $this->assertCount(1, $result);
        $this->assertEquals('user', $result[0]->role->value);
        $this->assertStringContainsString('helpful assistant', $result[0]->parts[0]->text);
    }

    /** @test */
    public function it_can_handle_conversation_with_alternating_roles()
    {
        // Create conversation history with alternating roles
        $messages = collect([
            Message::factory()->create([
                'conversation_id' => $this->conversation->id,
                'role' => MessageRole::ASSISTANT,
                'content' => 'Hello! How can I help you today?',
                'status' => MessageStatus::COMPLETED,
                'created_at' => now()->subMinutes(10),
            ]),
            Message::factory()->create([
                'conversation_id' => $this->conversation->id,
                'role' => MessageRole::USER,
                'content' => 'I need help with Laravel',
                'status' => MessageStatus::COMPLETED,
                'created_at' => now()->subMinutes(8),
            ]),
            Message::factory()->create([
                'conversation_id' => $this->conversation->id,
                'role' => MessageRole::ASSISTANT,
                'content' => 'Sure! What specific aspect of Laravel do you need help with?',
                'status' => MessageStatus::COMPLETED,
                'created_at' => now()->subMinutes(6),
            ]),
        ]);

        // Create current user message
        $currentMessage = Message::factory()->create([
            'conversation_id' => $this->conversation->id,
            'role' => MessageRole::USER,
            'content' => 'How do I create a migration?',
            'status' => MessageStatus::PENDING,
            'created_at' => now(),
        ]);

        // Create the job
        $job = new ProcessChatMessage([$this->bot, $currentMessage]);

        // Use reflection to access protected methods
        $reflection = new \ReflectionClass($job);
        
        $getContextMethod = $reflection->getMethod('getConversationContext');
        $getContextMethod->setAccessible(true);
        
        $prepareMethod = $reflection->getMethod('prepareApiContents');
        $prepareMethod->setAccessible(true);

        // Get conversation context (should exclude current message)
        $context = $getContextMethod->invoke($job);
        $this->assertCount(3, $context);

        // Prepare API contents
        $apiContents = $prepareMethod->invoke($job, $context);

        // Expected structure:
        // 1. System prompt (USER)
        // 2. First assistant message (MODEL)
        // 3. User message (USER)
        // 4. Second assistant message (MODEL)

        $this->assertCount(4, $apiContents);
        $this->assertEquals('user', $apiContents[0]->role->value); // System prompt
        $this->assertEquals('model', $apiContents[1]->role->value); // Assistant
        $this->assertEquals('user', $apiContents[2]->role->value); // User
        $this->assertEquals('model', $apiContents[3]->role->value); // Assistant
    }

    /** @test */
    public function it_can_handle_conversation_with_duplicate_roles()
    {
        // Create conversation history with duplicate roles
        $messages = collect([
            Message::factory()->create([
                'conversation_id' => $this->conversation->id,
                'role' => MessageRole::USER,
                'content' => 'First user message',
                'status' => MessageStatus::COMPLETED,
                'created_at' => now()->subMinutes(10),
            ]),
            Message::factory()->create([
                'conversation_id' => $this->conversation->id,
                'role' => MessageRole::USER,
                'content' => 'Second user message (duplicate)',
                'status' => MessageStatus::COMPLETED,
                'created_at' => now()->subMinutes(8),
            ]),
            Message::factory()->create([
                'conversation_id' => $this->conversation->id,
                'role' => MessageRole::ASSISTANT,
                'content' => 'Assistant response',
                'status' => MessageStatus::COMPLETED,
                'created_at' => now()->subMinutes(6),
            ]),
        ]);

        // Create current user message
        $currentMessage = Message::factory()->create([
            'conversation_id' => $this->conversation->id,
            'role' => MessageRole::USER,
            'content' => 'Current user message',
            'status' => MessageStatus::PENDING,
            'created_at' => now(),
        ]);

        // Create the job
        $job = new ProcessChatMessage([$this->bot, $currentMessage]);

        // Use reflection to access protected methods
        $reflection = new \ReflectionClass($job);
        
        $getContextMethod = $reflection->getMethod('getConversationContext');
        $getContextMethod->setAccessible(true);
        
        $prepareMethod = $reflection->getMethod('prepareApiContents');
        $prepareMethod->setAccessible(true);

        // Get conversation context
        $context = $getContextMethod->invoke($job);
        $this->assertCount(3, $context);

        // Prepare API contents
        $apiContents = $prepareMethod->invoke($job, $context);

        // Expected structure after role alternation fix:
        // 1. System prompt (USER)
        // 2. Assistant message (MODEL) - first USER message skipped due to alternation
        
        $this->assertCount(2, $apiContents);
        $this->assertEquals('user', $apiContents[0]->role->value); // System prompt
        $this->assertEquals('model', $apiContents[1]->role->value); // Assistant (duplicate users filtered out)
    }

    /** @test */
    public function it_logs_api_contents_structure_for_debugging()
    {
        Log::spy();

        // Create simple conversation
        $userMessage = Message::factory()->create([
            'conversation_id' => $this->conversation->id,
            'role' => MessageRole::USER,
            'content' => 'Test message',
            'status' => MessageStatus::PENDING,
        ]);

        // Create the job
        $job = new ProcessChatMessage([$this->bot, $userMessage]);

        // Use reflection to access protected methods
        $reflection = new \ReflectionClass($job);
        
        $getContextMethod = $reflection->getMethod('getConversationContext');
        $getContextMethod->setAccessible(true);
        
        $prepareMethod = $reflection->getMethod('prepareApiContents');
        $prepareMethod->setAccessible(true);

        // Get context and prepare contents
        $context = $getContextMethod->invoke($job);
        $apiContents = $prepareMethod->invoke($job, $context);

        // Assert log was called
        Log::shouldHaveReceived('info')
            ->with('API Contents Structure', \Mockery::type('array'))
            ->once();
    }

    /** @test */
    public function it_filters_only_user_and_assistant_messages()
    {
        // Create mixed message types
        Message::factory()->create([
            'conversation_id' => $this->conversation->id,
            'role' => MessageRole::SYSTEM,
            'content' => 'System message',
            'status' => MessageStatus::COMPLETED,
            'created_at' => now()->subMinutes(10),
        ]);

        Message::factory()->create([
            'conversation_id' => $this->conversation->id,
            'role' => MessageRole::USER,
            'content' => 'User message',
            'status' => MessageStatus::COMPLETED,
            'created_at' => now()->subMinutes(8),
        ]);

        Message::factory()->create([
            'conversation_id' => $this->conversation->id,
            'role' => MessageRole::TOOL,
            'content' => 'Tool message',
            'status' => MessageStatus::COMPLETED,
            'created_at' => now()->subMinutes(6),
        ]);

        Message::factory()->create([
            'conversation_id' => $this->conversation->id,
            'role' => MessageRole::ASSISTANT,
            'content' => 'Assistant message',
            'status' => MessageStatus::COMPLETED,
            'created_at' => now()->subMinutes(4),
        ]);

        // Create current message
        $currentMessage = Message::factory()->create([
            'conversation_id' => $this->conversation->id,
            'role' => MessageRole::USER,
            'content' => 'Current message',
            'status' => MessageStatus::PENDING,
            'created_at' => now(),
        ]);

        // Create the job
        $job = new ProcessChatMessage([$this->bot, $currentMessage]);

        // Use reflection to access protected method
        $reflection = new \ReflectionClass($job);
        $getContextMethod = $reflection->getMethod('getConversationContext');
        $getContextMethod->setAccessible(true);

        // Get conversation context
        $context = $getContextMethod->invoke($job);

        // Should only include user and assistant messages (2 out of 4)
        $this->assertCount(2, $context);

        $roles = $context->pluck('role')->map(fn($role) => $role->value)->toArray();
        $this->assertContains('user', $roles);
        $this->assertContains('assistant', $roles);
        $this->assertNotContains('system', $roles);
        $this->assertNotContains('tool', $roles);
    }

    /** @test */
    public function it_can_process_real_conversation_scenario()
    {
        // Create a realistic conversation scenario
        $messages = [
            // User starts conversation
            Message::factory()->create([
                'conversation_id' => $this->conversation->id,
                'role' => MessageRole::USER,
                'content' => 'Hello, I need help with my Laravel project',
                'status' => MessageStatus::COMPLETED,
                'created_at' => now()->subMinutes(20),
            ]),
            // AI responds
            Message::factory()->create([
                'conversation_id' => $this->conversation->id,
                'role' => MessageRole::ASSISTANT,
                'content' => 'Hello! I\'d be happy to help you with your Laravel project. What specific issue are you facing?',
                'status' => MessageStatus::COMPLETED,
                'created_at' => now()->subMinutes(18),
            ]),
            // User asks specific question
            Message::factory()->create([
                'conversation_id' => $this->conversation->id,
                'role' => MessageRole::USER,
                'content' => 'I\'m having trouble with database migrations. How do I create a new migration?',
                'status' => MessageStatus::COMPLETED,
                'created_at' => now()->subMinutes(15),
            ]),
            // AI provides detailed answer
            Message::factory()->create([
                'conversation_id' => $this->conversation->id,
                'role' => MessageRole::ASSISTANT,
                'content' => 'To create a new migration in Laravel, you can use the artisan command: `php artisan make:migration create_table_name`. This will generate a new migration file in the database/migrations directory.',
                'status' => MessageStatus::COMPLETED,
                'created_at' => now()->subMinutes(12),
            ]),
        ];

        // Create current user message that will trigger the job
        $currentMessage = Message::factory()->create([
            'conversation_id' => $this->conversation->id,
            'role' => MessageRole::USER,
            'content' => 'Can you show me an example of a migration file?',
            'status' => MessageStatus::PENDING,
            'created_at' => now(),
        ]);

        // Create and test the job
        $job = new ProcessChatMessage([$this->bot, $currentMessage]);

        // Use reflection to test the complete flow
        $reflection = new \ReflectionClass($job);

        $getContextMethod = $reflection->getMethod('getConversationContext');
        $getContextMethod->setAccessible(true);

        $prepareMethod = $reflection->getMethod('prepareApiContents');
        $prepareMethod->setAccessible(true);

        // Test conversation context retrieval
        $context = $getContextMethod->invoke($job);
        $this->assertCount(4, $context); // Should get all 4 previous messages

        // Test API contents preparation
        $apiContents = $prepareMethod->invoke($job, $context);

        // Expected structure:
        // 1. System prompt (USER)
        // 2. User message 1 (USER) - but this will be skipped due to alternation
        // 3. Assistant message 1 (MODEL) - included
        // 4. User message 2 (USER) - included
        // 5. Assistant message 2 (MODEL) - included

        // Due to role alternation starting with MODEL after system prompt,
        // the first user message will be skipped to maintain alternation
        $this->assertCount(4, $apiContents);

        // Verify the structure
        $this->assertEquals('user', $apiContents[0]->role->value); // System prompt
        $this->assertEquals('model', $apiContents[1]->role->value); // Assistant response 1
        $this->assertEquals('user', $apiContents[2]->role->value); // User question 2
        $this->assertEquals('model', $apiContents[3]->role->value); // Assistant response 2

        // Verify content
        $this->assertStringContainsString('helpful assistant', $apiContents[0]->parts[0]->text);
        $this->assertStringContainsString('happy to help', $apiContents[1]->parts[0]->text);
        $this->assertStringContainsString('database migrations', $apiContents[2]->parts[0]->text);
        $this->assertStringContainsString('artisan make:migration', $apiContents[3]->parts[0]->text);
    }
}

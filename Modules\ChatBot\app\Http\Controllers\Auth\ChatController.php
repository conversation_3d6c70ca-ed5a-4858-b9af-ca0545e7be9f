<?php

namespace Modules\ChatBot\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\ChatBot\Facades\ChatFacade;
use Modules\ChatBot\Models\Bot;
use Modules\Core\Traits\ResponseTrait;

class ChatController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {

    }

    /**
     * Display a listing of user's accessible bots.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            return $this->successResponse(ChatFacade::getActiveChatBots());
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }
}

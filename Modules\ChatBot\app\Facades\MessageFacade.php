<?php

namespace Modules\ChatBot\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static \Illuminate\Pagination\LengthAwarePaginator getConversationMessages(string $conversationUuid, int $perPage = 50)
 * @method static \Modules\ChatBot\Models\Message createUserMessage(array $data)
 * @method static \Modules\ChatBot\Models\Message generateAIResponse(\Modules\ChatBot\Models\Conversation $conversation, array $options = [])
 * @method static \Modules\ChatBot\Models\Message updateMessage(\Modules\ChatBot\Models\Message $message, array $data)
 * @method static bool deleteMessage(\Modules\ChatBot\Models\Message $message)
 * @method static \Modules\ChatBot\Models\Message retryMessage(\Modules\ChatBot\Models\Message $message)
 * @method static array buildConversationContext(\Modules\ChatBot\Models\Conversation $conversation)
 * @method static array getMessageStats(array $filters = [])
 *
 * @see \Modules\ChatBot\Services\MessageService
 */
class MessageFacade extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'chatbot.message';
    }
}

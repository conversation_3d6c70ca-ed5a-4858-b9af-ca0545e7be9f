<?php return array (
  'providers' => 
  array (
    0 => 'Modules\\ActiveCode\\Providers\\ActiveCodeServiceProvider',
    1 => 'Modules\\Auth\\Providers\\AuthServiceProvider',
    2 => 'Modules\\Billing\\Providers\\BillingServiceProvider',
    3 => 'Modules\\Blog\\Providers\\BlogServiceProvider',
    4 => 'Modules\\ChatBot\\Providers\\ChatBotServiceProvider',
    5 => 'Modules\\Core\\Providers\\CoreServiceProvider',
    6 => 'Modules\\Currency\\Providers\\CurrencyServiceProvider',
    7 => 'Modules\\Language\\Providers\\LanguageServiceProvider',
    8 => 'Modules\\Location\\Providers\\LocationServiceProvider',
    9 => 'Modules\\ModelAI\\Providers\\ModelAIServiceProvider',
    10 => 'Modules\\Notification\\Providers\\NotificationServiceProvider',
    11 => 'Modules\\Organization\\Providers\\OrganizationServiceProvider',
    12 => 'Modules\\Page\\Providers\\PageServiceProvider',
    13 => 'Modules\\Payment\\Providers\\PaymentServiceProvider',
    14 => 'Modules\\Role\\Providers\\RoleServiceProvider',
    15 => 'Modules\\Setting\\Providers\\SettingServiceProvider',
    16 => 'Modules\\Theme\\Providers\\ThemeServiceProvider',
    17 => 'Modules\\Translation\\Providers\\TranslationServiceProvider',
    18 => 'Modules\\User\\Providers\\UserServiceProvider',
  ),
  'eager' => 
  array (
    0 => 'Modules\\ActiveCode\\Providers\\ActiveCodeServiceProvider',
    1 => 'Modules\\Auth\\Providers\\AuthServiceProvider',
    2 => 'Modules\\Billing\\Providers\\BillingServiceProvider',
    3 => 'Modules\\Blog\\Providers\\BlogServiceProvider',
    4 => 'Modules\\ChatBot\\Providers\\ChatBotServiceProvider',
    5 => 'Modules\\Core\\Providers\\CoreServiceProvider',
    6 => 'Modules\\Currency\\Providers\\CurrencyServiceProvider',
    7 => 'Modules\\Language\\Providers\\LanguageServiceProvider',
    8 => 'Modules\\Location\\Providers\\LocationServiceProvider',
    9 => 'Modules\\ModelAI\\Providers\\ModelAIServiceProvider',
    10 => 'Modules\\Notification\\Providers\\NotificationServiceProvider',
    11 => 'Modules\\Organization\\Providers\\OrganizationServiceProvider',
    12 => 'Modules\\Page\\Providers\\PageServiceProvider',
    13 => 'Modules\\Payment\\Providers\\PaymentServiceProvider',
    14 => 'Modules\\Role\\Providers\\RoleServiceProvider',
    15 => 'Modules\\Setting\\Providers\\SettingServiceProvider',
    16 => 'Modules\\Theme\\Providers\\ThemeServiceProvider',
    17 => 'Modules\\Translation\\Providers\\TranslationServiceProvider',
    18 => 'Modules\\User\\Providers\\UserServiceProvider',
  ),
  'deferred' => 
  array (
  ),
);
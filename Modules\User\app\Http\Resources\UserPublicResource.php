<?php

namespace Modules\User\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * User Public Resource
 * 
 * This resource is used for public user information display
 * using UUID instead of internal ID for security.
 */
class UserPublicResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'uuid' => $this->uuid,
            'username' => $this->username,
            'full_name' => $this->full_name,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'avatar_url' => $this->avatar_url,
            'gender' => [
                'value' => $this->gender->value,
                'label' => $this->gender->label(),
                'icon' => $this->gender->icon(),
            ],
            'status' => [
                'value' => $this->status->value,
                'label' => $this->status->label(),
                'css_class' => $this->status->cssClass(),
            ],
            'is_verified' => $this->is_verified,
            'newsletter_subscribed' => $this->newsletter_subscribed,
            'location' => [
                'country' => $this->whenLoaded('country', function () {
                    return [
                        'id' => $this->country->id,
                        'name' => $this->country->name,
                        'code' => $this->country->code,
                    ];
                }),
                'geo_division' => $this->whenLoaded('geoDivision', function () {
                    return [
                        'id' => $this->geoDivision->id,
                        'name' => $this->geoDivision->name,
                        'type' => $this->geoDivision->type,
                    ];
                }),
            ],
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }
}

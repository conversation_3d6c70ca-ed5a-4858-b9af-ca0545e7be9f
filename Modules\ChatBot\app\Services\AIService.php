<?php

namespace Modules\ChatBot\Services;

use Exception;
use Gemini\Data\Content;
use Gemini\Enums\Role;
use Modules\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\Message;
use Modules\ModelAI\Facades\ModelAIFacade;
use Modules\ModelAI\Models\ModelAI;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Modules\ModelAI\Models\ModelProvider;
use OpenAI\Laravel\Facades\OpenAI;
use Gemini\Laravel\Facades\Gemini;

class AIService
{

    /**
     * Generate general prompts using AI providers
     * @throws Exception
     */
    public function botGeneralPrompts(): ?string
    {
        // Get the appropriate prompt template
        $prompt = $this->getPromptTemplate();

        $model = $this->getDefaultModel();

        return $this->callAIWithModel($prompt, $model);
    }

    /**
     * Get default model from user preferences or system default
     */
    private function getDefaultModel(): ModelAI
    {
        return ModelAIFacade::getModelAIDefault();
    }

    /**
     * Call AI API using ModelAI configuration
     */
    private function callAIWithModel(string $prompt, ModelAI $model): ?string
    {
        $provider = $model->provider;
        try {
            return match ($provider->key) {
                'google', 'gemini' => $this->callGeminiWithLibrary($prompt, $model),
                default => $this->callOpenAIWithLibrary($prompt, $model), // OpenAI library supports multiple providers
            };
        } catch (Exception $e) {
            Log::error('AI Model API Error', [
                'model_id' => $model->id,
                'model_key' => $model->key,
                'provider' => $provider->key,
                'error' => $e->getMessage(),
                'prompt_length' => strlen($prompt),
            ]);
            return null;
        }
    }

    /**
     * Call Gemini API using google-gemini-php/laravel library
     */
    private function callGeminiWithLibrary(string $prompt, ModelAI $model): ?string
    {
        try {
            $provider = $model->provider;
            if (!$provider->api_key) {
                throw new Exception('Vui lòng cấu hình API Key cho Gemini trong ModelProvider');
            }

            config()->set(['gemini.api_key' => $provider->api_key]);
            $gemini = Gemini::generativeModel(model: $model->key)->generateContent($prompt);
            $text = $gemini->text();

            if (empty($text)) {
                Log::warning('Empty response from Gemini API', [
                    'model_id' => $model->id,
                    'model_key' => $model->key,
                    'prompt_length' => strlen($prompt),
                ]);

                throw new Exception('Gemini API trả về response trống.');
            }

            return $text;

        } catch (Exception $e) {
            Log::error('Gemini Library Error', [
                'model_id' => $model->id,
                'model_key' => $model->key,
                'error' => $e->getMessage(),
                'prompt_length' => strlen($prompt),
            ]);
            return null;
        }
    }

    /**
     * Call OpenAI API using openai-php/laravel library (supports multiple providers)
     */
    private function callOpenAIWithLibrary(string $prompt, ModelAI $model): ?string
    {
        try {
            // Get API key and base URL from ModelProvider
            $apiKey = $model->provider->getApiKey();
            $baseUrl = $model->provider->base_url;

            if (!$apiKey) {
                throw new Exception("Vui lòng cấu hình API Key cho {$model->provider->name} trong ModelProvider");
            }

            // Create OpenAI client with custom configuration for different providers
            config()->set(['openai.api_key' => $apiKey,
                'openai.base_url' => $baseUrl
            ]);

            // Generate content using chat completions
            $openai = OpenAI::chat()->create([
                'model' => $model->key,
                'messages' => [
                    ['role' => 'user', 'content' => $prompt],
                ],
                'max_tokens' => 2048,
                'temperature' => 0.7,
            ]);

            // Extract text from response
            return $openai->choices[0]->message->content ?? null;

        } catch (Exception $e) {
            Log::error('OpenAI Library Error', [
                'model_id' => $model->id,
                'model_key' => $model->key,
                'provider' => $model->provider->key,
                'error' => $e->getMessage(),
                'prompt_length' => strlen($prompt),
            ]);
            return null;
        }
    }

    /**
     * Get prompt template based on type
     */
    private function getPromptTemplate(): ?string
    {
        $promptType = request()->input('type');
        return match ($promptType) {
            'system_prompt' => $this->userSystemPrompts(request()->input('role')),
            'greeting_message' => $this->botGreetingMessagePrompt(request()->input('name')),
            'starting_message' => $this->userStartingMessagePrompt(request()->input('system_prompt')),
            default => null,
        };
    }

    private function userSystemPrompts(string $botRole): string
    {
        return "Bạn là một chuyên gia tạo system prompt cho chatbot. Dựa trên vai trò người dùng cung cấp là \"{$botRole}\", hãy tạo ra một system prompt hoàn chỉnh bằng tiếng Việt.
                    **QUAN TRỌNG:** Chỉ trả về nội dung của system prompt, không bao gồm bất kỳ lời chào, giải thích, hay định dạng markdown nào khác. Bắt đầu trực tiếp với nội dung prompt.

                    Cấu trúc của prompt cần có:
                    1.  **Persona:** Mô tả chi tiết vai trò, tính cách (ví dụ: thân thiện, chuyên nghiệp, am hiểu sư phạm).
                    2.  **Instructions:** Các quy tắc và hướng dẫn cụ thể mà AI phải tuân theo (ví dụ: luôn tuân thủ cấu trúc giáo án chuẩn, không được bịa đặt thông tin, luôn xưng \"tôi\").
                    3.  **Capabilities:** Liệt kê các công việc mà AI có thể làm (ví dụ: soạn giáo án, gợi ý hoạt động, đề xuất câu hỏi).
                    4.  **Constraints:** Liệt kê các việc AI không được làm (ví dụ: không đưa ra đánh giá cá nhân, không cung cấp thông tin ngoài chương trình).";
    }

    private function botGreetingMessagePrompt(string $name): string
    {
        return "Viết một câu chào mừng ngắn gọn, thân thiện (dưới 15 từ) bằng tiếng Việt cho một chatbot tên là \"{$name}\". Chỉ trả về duy nhất câu chào.";
    }

    private function userStartingMessagePrompt(string $systemPrompt): string
    {
        return "Dựa vào prompt hệ thống sau của một chatbot: \"{$systemPrompt}\". Hãy đề xuất 4 câu hỏi ngắn gọn mà người dùng có thể hỏi. Yêu cầu: Chỉ trả về một mảng JSON chứa các chuỗi ký tự. Ví dụ: [\"Học phí bao nhiêu?\", \"Trường có những ngành nào?\"]";
    }

    public function getAssistantPrompts(Bot $bot, Message $question, Message $assistant, $messages): ?bool
    {
        $provider = $bot->aiModel->provider;
        $model = $bot->aiModel;
        $systemPrompt = $bot->system_prompt;

        try {
            return match ($provider->key) {
                'google', 'gemini' => $this->getGoogleProviderCallback($question, $assistant, $systemPrompt, $provider, $model, $messages),
                default => $this->getOpenAIProviderCallback() // OpenAI library supports multiple providers
            };

        } catch (Exception $e) {
            Log::error('AI Model API Error', [
                'model_id' => $model->id,
                'model_key' => $model->key,
                'provider' => $provider->key,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    private function getGoogleProviderCallback(Message $question, Message $assistant, string $systemPrompt, ModelProvider $provider, ModelAI $model, mixed $messages): bool
    {
        $contents = [
            Content::parse($systemPrompt, role: Role::USER)
        ];

        $parseMessages = $messages->map(function ($msg) {
            return Content::parse($msg->content, $msg->role == MessageRole::USER ? Role::USER : Role::MODEL);
        })->toArray();

        $contents = array_merge($contents, $parseMessages);

        config()->set(['gemini.api_key' => $provider->api_key]);
        $gemini = Gemini::generativeModel($model->key)
            ->generateContent(...$contents);

        if (empty($gemini->text())) {
            Log::warning('Empty response from Gemini API---------->', [$gemini]);
            return false;
        }

        $response = $gemini->toArray();
        $usage = $response['usageMetadata'] ?? [];

        $question->update([
            'prompt_tokens' => $usage['promptTokenCount'] ?? 0,
            'status' => 'completed'
        ]);

        $assistant->update([
            'content' => $gemini->text(),
            'status' => 'completed',
            'prompt_tokens' => $usage['promptTokenCount'] ?? 0,
            'completion_tokens' => $usage['candidatesTokenCount'] ?? 0,
            'total_tokens' => $usage['totalTokenCount'] ?? 0
        ]);


        logger()->info('------------------------------>:::::', $response);

        return true;
    }

    private function getOpenAIProviderCallback(): false
    {
        return false;
    }

}

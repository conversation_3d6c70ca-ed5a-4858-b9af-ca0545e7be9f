<?php

namespace Modules\ChatBot\Jobs;

use Exception;
use Gemini\Data\Content;
use Gemini\Enums\Role;
use Gemini\Laravel\Facades\Gemini;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Modules\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Facades\AIFacade;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\Message;
use Modules\ModelAI\Models\ModelProvider;

class ProcessChatMessage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected Message $message;
    protected Message $question;
    protected Bot $bot;

    /**
     * Create a new job instance.
     */
    public function __construct(array $payload)
    {
        $this->bot = $payload[0];
        $this->message = $payload[1];
        $this->question = $payload[2];
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // 1. <PERSON><PERSON><PERSON> lịch sử tin nhắn để làm ngữ cảnh
            $messages = $this->getConversationContext();

            // 3. Gọi Gemini API để lấy nội dung phản hồi
            AIFacade::getAssistantPrompts($this->bot, $this->message, $this->question, $messages);

        } catch (Exception $e) {
            $this->message->update(['status' => 'failed']);
            report($e);
            throw $e;
        }
    }

    /**
     * Chuẩn bị mảng nội dung cho lệnh gọi Gemini API.
     */
    protected function prepareApiContents(Collection $messages): array
    {
        $contents = [
            Content::parse($this->bot->system_prompt, role: Role::USER)
        ];

        $parsedMessages = $messages->map(function (Message $msg) {
            return Content::parse($msg->content, $msg->role === MessageRole::USER ? Role::USER : Role::MODEL);
        })->toArray();

        return array_merge($contents, $parsedMessages);
    }

    /**
     * Gọi Gemini API và trả về văn bản phản hồi.
     * Phương thức này là `protected` để có thể dễ dàng "mock" trong khi test.
     *
     * @throws Exception
     */
    protected function getAiResponse(array $contents): string
    {
        $model = $this->bot->aiModel;
        $provider = $this->bot->aiModel->provider;

        config()->set('gemini.api_key', $provider->api_key);
        $response = Gemini::generativeModel($model->key)->generateContent(...$contents);

        $text = $response->text();

        if (empty($text)) {
            Log::warning('Phản hồi trống từ Gemini API.', [
                'bot_id' => $this->bot->id,
                'content' => [...$contents],
            ]);
            // throw new Exception('Gemini API trả về response trống.');
        }

        return $text;
    }

    /**
     * Lưu phản hồi của AI vào cơ sở dữ liệu.
     */
    protected function saveAiResponse(string $responseText): void
    {
        $this->question->update([
            'status' => 'completed'
        ]);

        $this->message->update([
            'status' => 'completed',
            'content' => $responseText
        ]);


    }

    /**
     * Lấy 10 tin nhắn cuối cùng trong cuộc hội thoại để làm ngữ cảnh.
     */
    private function getConversationContext(): Collection
    {
        return Message::where('conversation_id', $this->message->conversation_id)
            ->where('id', '!=', $this->message->id)
            ->where('role', 'in', ['user', 'assistant'])
            ->whereIn('status', ['completed', 'pending'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->reverse();
    }

    /**
     * Xử lý khi job thất bại.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('ProcessChatMessage Job Failed', [
            'message_id' => $this->message->id,
            'bot_id' => $this->bot->id,
            'error' => $exception->getMessage(),
        ]);
    }
}

<?php

namespace Modules\User\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\User\Models\User;
use Modules\User\Http\Resources\UserPublicResource;

/**
 * Public User Controller for UUID-based operations
 * 
 * This controller handles public user operations using UUID instead of ID
 * for better security and privacy.
 */
class UserPublicController extends Controller
{
    /**
     * Get user profile by UUID.
     * 
     * @param string $uuid
     * @return JsonResponse
     */
    public function show(string $uuid): JsonResponse
    {
        try {
            $user = User::findByUuidOrFail($uuid);
            
            return response()->json([
                'success' => true,
                'data' => new UserPublicResource($user),
                'message' => 'User profile retrieved successfully'
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'User not found'
            ], 404);
        }
    }

    /**
     * Check if user exists by UUID.
     * 
     * @param string $uuid
     * @return JsonResponse
     */
    public function exists(string $uuid): JsonResponse
    {
        $exists = User::findByUuid($uuid) !== null;
        
        return response()->json([
            'success' => true,
            'data' => [
                'exists' => $exists,
                'uuid' => $uuid
            ],
            'message' => $exists ? 'User exists' : 'User not found'
        ]);
    }

    /**
     * Get user's public information by UUID.
     * 
     * @param string $uuid
     * @return JsonResponse
     */
    public function publicInfo(string $uuid): JsonResponse
    {
        $user = User::findByUuid($uuid);
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'uuid' => $user->uuid,
                'full_name' => $user->full_name,
                'avatar_url' => $user->avatar_url,
                'status' => $user->status->label(),
                'is_verified' => $user->is_verified,
                'created_at' => $user->created_at->format('Y-m-d H:i:s')
            ],
            'message' => 'User public information retrieved successfully'
        ]);
    }
}

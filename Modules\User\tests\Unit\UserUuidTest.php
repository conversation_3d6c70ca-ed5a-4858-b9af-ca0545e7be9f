<?php

namespace Modules\User\Tests\Unit;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Str;
use Modules\User\Models\User;
use Tests\TestCase;

class UserUuidTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_automatically_generates_uuid_when_creating_user()
    {
        $user = User::factory()->create();

        $this->assertNotNull($user->uuid);
        $this->assertTrue(Str::isUuid($user->uuid));
    }

    /** @test */
    public function it_can_find_user_by_uuid()
    {
        $user = User::factory()->create();

        $foundUser = User::findByUuid($user->uuid);

        $this->assertNotNull($foundUser);
        $this->assertEquals($user->id, $foundUser->id);
    }

    /** @test */
    public function it_returns_null_when_user_not_found_by_uuid()
    {
        $nonExistentUuid = (string) Str::uuid();

        $foundUser = User::findByUuid($nonExistentUuid);

        $this->assertNull($foundUser);
    }

    /** @test */
    public function it_throws_exception_when_user_not_found_by_uuid_or_fail()
    {
        $nonExistentUuid = (string) Str::uuid();

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        User::findByUuidOrFail($nonExistentUuid);
    }

    /** @test */
    public function it_can_scope_by_uuid()
    {
        $user = User::factory()->create();

        $foundUser = User::byUuid($user->uuid)->first();

        $this->assertNotNull($foundUser);
        $this->assertEquals($user->id, $foundUser->id);
    }

    /** @test */
    public function uuid_is_unique_across_users()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        $this->assertNotEquals($user1->uuid, $user2->uuid);
    }

    /** @test */
    public function uuid_is_included_in_fillable_attributes()
    {
        $user = new User();

        $this->assertContains('uuid', $user->getFillable());
    }

    /** @test */
    public function uuid_is_cast_to_string()
    {
        $user = User::factory()->create();

        $this->assertIsString($user->uuid);
    }

    /** @test */
    public function it_does_not_override_manually_set_uuid()
    {
        $customUuid = (string) Str::uuid();
        
        $user = User::factory()->create(['uuid' => $customUuid]);

        $this->assertEquals($customUuid, $user->uuid);
    }
}

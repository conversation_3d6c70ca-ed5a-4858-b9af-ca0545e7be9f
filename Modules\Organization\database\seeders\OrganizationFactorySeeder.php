<?php

namespace Modules\Organization\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\User\Models\User;
use Modules\Organization\Models\Organization;
use Modules\Organization\Models\OrganizationMember;
use Modules\Organization\Models\OrganizationInvitation;

class OrganizationFactorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     * Tạo dữ liệu mẫu sử dụng factory để test
     */
    public function run(): void
    {
        $this->command->info('Creating organizations using factories...');
        
        // Tạo một số users để làm owner
        $users = User::factory()->count(10)->create();
        
        // Tạo các company chính
        $companies = Organization::factory()
            ->count(3)
            ->company()
            ->active()
            ->sequence(
                ['visibility' => 'public'],
                ['visibility' => 'internal'],
                ['visibility' => 'private']
            )
            ->create();

        // Tạo teams cho mỗi company
        foreach ($companies as $company) {
            $teams = Organization::factory()
                ->count(rand(2, 4))
                ->team()
                ->parent($company)
                ->active()
                ->create();

            // Thêm owner của company làm admin member
            OrganizationMember::factory()
                ->forOrganizationAndUser($company, $company->owner)
                ->admin()
                ->create();

            // Thêm một số members cho company
            $companyMembers = $users->random(rand(3, 6));
            foreach ($companyMembers as $user) {
                if ($user->id !== $company->owner_id) {
                    OrganizationMember::factory()
                        ->forOrganizationAndUser($company, $user)
                        ->create();
                }
            }

            // Thêm members cho các teams
            foreach ($teams as $team) {
                // Thêm owner của team làm admin
                OrganizationMember::factory()
                    ->forOrganizationAndUser($team, $team->owner)
                    ->admin()
                    ->create();

                // Thêm một số members cho team
                $teamMembers = $users->random(rand(2, 4));
                foreach ($teamMembers as $user) {
                    if ($user->id !== $team->owner_id) {
                        OrganizationMember::factory()
                            ->forOrganizationAndUser($team, $user)
                            ->create();
                    }
                }
            }
        }

        // Tạo một số organizations độc lập (không có parent)
        $independentOrgs = Organization::factory()
            ->count(5)
            ->sequence(
                ['type' => 'company'],
                ['type' => 'team'],
                ['type' => 'company'],
                ['type' => 'team'],
                ['type' => 'company']
            )
            ->active()
            ->create();

        foreach ($independentOrgs as $org) {
            // Thêm owner làm admin
            OrganizationMember::factory()
                ->forOrganizationAndUser($org, $org->owner)
                ->admin()
                ->create();

            // Thêm một số members
            $members = $users->random(rand(1, 3));
            foreach ($members as $user) {
                if ($user->id !== $org->owner_id) {
                    OrganizationMember::factory()
                        ->forOrganizationAndUser($org, $user)
                        ->create();
                }
            }
        }

        // Tạo một số invitations
        $allOrganizations = Organization::all();
        $allUsers = User::all();

        // Tạo pending invitations
        foreach ($allOrganizations->random(min(10, $allOrganizations->count())) as $org) {
            $inviter = $org->organizationMembers()->where('role', 'admin')->first()?->user ?? $org->owner;
            
            OrganizationInvitation::factory()
                ->count(rand(1, 3))
                ->forOrganization($org)
                ->invitedBy($inviter)
                ->create();
        }

        // Tạo một số expired invitations
        foreach ($allOrganizations->random(min(5, $allOrganizations->count())) as $org) {
            $inviter = $org->organizationMembers()->where('role', 'admin')->first()?->user ?? $org->owner;
            
            OrganizationInvitation::factory()
                ->count(rand(1, 2))
                ->expired()
                ->forOrganization($org)
                ->invitedBy($inviter)
                ->create();
        }

        $this->command->info('Factory seeding completed!');
        $this->command->info('Total organizations: ' . Organization::count());
        $this->command->info('Total members: ' . OrganizationMember::count());
        $this->command->info('Total invitations: ' . OrganizationInvitation::count());
    }
}

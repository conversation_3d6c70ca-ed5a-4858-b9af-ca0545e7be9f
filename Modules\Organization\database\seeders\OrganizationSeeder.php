<?php

namespace Modules\Organization\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use Modules\User\Models\User;
use Modules\Organization\Models\Organization;
use Modules\Organization\Models\OrganizationMember;
use Modules\Organization\Models\OrganizationInvitation;

class OrganizationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Tạo users mẫu nếu chưa có
        $users = $this->createSampleUsers();
        
        // Tạo organizations mẫu
        $organizations = $this->createSampleOrganizations($users);

        // Tạo invitations mẫu
        $this->createSampleInvitations($organizations, $users);
    }

    /**
     * Tạo users mẫu để làm owner và member
     */
    private function createSampleUsers(): array
    {
        $users = [];
        
        // Tạo admin user
        $users['admin'] = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'username' => 'org_admin',
                'first_name' => 'System',
                'last_name' => 'Admin',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );

        // Tạo các user khác
        $userEmails = [
            '<EMAIL>' => ['<PERSON>', 'Doe'],
            '<EMAIL>' => ['Jane', 'Smith'],
            '<EMAIL>' => ['Mike', '<PERSON>'],
            '<EMAIL>' => ['Sarah', 'Wilson'],
            '<EMAIL>' => ['David', 'Brown'],
        ];

        foreach ($userEmails as $email => $names) {
            $username = strtolower(str_replace('.', '_', explode('@', $email)[0]));
            $key = strtolower($names[0] . '_' . $names[1]);
            $users[$key] = User::firstOrCreate(
                ['email' => $email],
                [
                    'username' => $username,
                    'first_name' => $names[0],
                    'last_name' => $names[1],
                    'password' => bcrypt('password'),
                    'email_verified_at' => now(),
                ]
            );
        }

        return $users;
    }

    /**
     * Tạo organizations mẫu với cấu trúc phân cấp
     */
    private function createSampleOrganizations(array $users): array
    {
        // 1. Tạo company chính (parent organization)
        $mainCompany = Organization::firstOrCreate(
            ['slug' => 'acme-corporation'],
            [
                'uuid' => Str::uuid(),
                'type' => 'company',
                'owner_id' => $users['admin']->id,
                'name' => 'ACME Corporation',
                'description' => 'A leading technology company specializing in innovative solutions.',
                'email' => '<EMAIL>',
                'phone' => '******-0123',
                'website' => 'https://acme.com',
                'address' => '123 Tech Street, Silicon Valley, CA',
                'postal_code' => '94000',
                'visibility' => 'public',
                'status' => 'active',
                'timezone' => 'America/Los_Angeles',
                'settings' => [
                    'notifications_enabled' => true,
                    'public_profile' => true,
                    'allow_member_invites' => true,
                ],
            ]
        );

        // Thêm owner làm admin member
        OrganizationMember::firstOrCreate(
            [
                'organization_id' => $mainCompany->id,
                'user_id' => $users['admin']->id,
            ],
            ['role' => 'admin']
        );

        // 2. Tạo các team con (child organizations)
        $teams = [
            [
                'name' => 'Engineering Team',
                'slug' => 'acme-engineering',
                'description' => 'Software development and engineering team.',
                'owner' => $users['john_doe'],
                'members' => [
                    ['user' => $users['john_doe'], 'role' => 'admin'],
                    ['user' => $users['jane_smith'], 'role' => 'editor'],
                    ['user' => $users['mike_johnson'], 'role' => 'member'],
                ]
            ],
            [
                'name' => 'Marketing Team',
                'slug' => 'acme-marketing',
                'description' => 'Marketing and communications team.',
                'owner' => $users['sarah_wilson'],
                'members' => [
                    ['user' => $users['sarah_wilson'], 'role' => 'admin'],
                    ['user' => $users['david_brown'], 'role' => 'editor'],
                    ['user' => $users['jane_smith'], 'role' => 'viewer'],
                ]
            ],
            [
                'name' => 'Sales Team',
                'slug' => 'acme-sales',
                'description' => 'Sales and business development team.',
                'owner' => $users['david_brown'],
                'members' => [
                    ['user' => $users['david_brown'], 'role' => 'admin'],
                    ['user' => $users['mike_johnson'], 'role' => 'editor'],
                ]
            ],
        ];

        foreach ($teams as $teamData) {
            $team = Organization::firstOrCreate(
                ['slug' => $teamData['slug']],
                [
                    'uuid' => Str::uuid(),
                    'parent_id' => $mainCompany->id,
                    'type' => 'team',
                    'owner_id' => $teamData['owner']->id,
                    'name' => $teamData['name'],
                    'description' => $teamData['description'],
                    'visibility' => 'internal',
                    'status' => 'active',
                    'timezone' => 'America/Los_Angeles',
                    'settings' => [
                        'notifications_enabled' => true,
                        'public_profile' => false,
                        'allow_member_invites' => false,
                    ],
                ]
            );

            // Thêm members cho team
            foreach ($teamData['members'] as $memberData) {
                OrganizationMember::firstOrCreate(
                    [
                        'organization_id' => $team->id,
                        'user_id' => $memberData['user']->id,
                    ],
                    ['role' => $memberData['role']]
                );
            }
        }

        // 3. Tạo một company độc lập khác
        $startupCompany = Organization::firstOrCreate(
            ['slug' => 'tech-startup'],
            [
                'uuid' => Str::uuid(),
                'type' => 'company',
                'owner_id' => $users['jane_smith']->id,
                'name' => 'Tech Startup Inc.',
                'description' => 'An innovative startup focused on AI and machine learning.',
                'email' => '<EMAIL>',
                'phone' => '******-0456',
                'website' => 'https://techstartup.com',
                'address' => '456 Innovation Blvd, Austin, TX',
                'postal_code' => '78701',
                'visibility' => 'private',
                'status' => 'active',
                'timezone' => 'America/Chicago',
                'settings' => [
                    'notifications_enabled' => true,
                    'public_profile' => false,
                    'allow_member_invites' => true,
                ],
            ]
        );

        // Thêm members cho startup
        $startupMembers = [
            ['user' => $users['jane_smith'], 'role' => 'admin'],
            ['user' => $users['mike_johnson'], 'role' => 'editor'],
            ['user' => $users['sarah_wilson'], 'role' => 'member'],
        ];

        foreach ($startupMembers as $memberData) {
            OrganizationMember::firstOrCreate(
                [
                    'organization_id' => $startupCompany->id,
                    'user_id' => $memberData['user']->id,
                ],
                ['role' => $memberData['role']]
            );
        }

        // 4. Tạo một team con cho startup
        $startupDevTeam = Organization::firstOrCreate(
            ['slug' => 'startup-dev-team'],
            [
                'uuid' => Str::uuid(),
                'parent_id' => $startupCompany->id,
                'type' => 'team',
                'owner_id' => $users['mike_johnson']->id,
                'name' => 'Development Team',
                'description' => 'Core development team for the startup.',
                'visibility' => 'private',
                'status' => 'active',
                'timezone' => 'America/Chicago',
                'settings' => [
                    'notifications_enabled' => true,
                    'public_profile' => false,
                    'allow_member_invites' => false,
                ],
            ]
        );

        // Thêm members cho dev team
        OrganizationMember::firstOrCreate(
            [
                'organization_id' => $startupDevTeam->id,
                'user_id' => $users['mike_johnson']->id,
            ],
            ['role' => 'admin']
        );

        OrganizationMember::firstOrCreate(
            [
                'organization_id' => $startupDevTeam->id,
                'user_id' => $users['david_brown']->id,
            ],
            ['role' => 'member']
        );

        $this->command->info('Organizations and members seeded successfully!');
        $this->command->info('Created organizations:');
        $this->command->info('- ACME Corporation (parent)');
        $this->command->info('  - Engineering Team');
        $this->command->info('  - Marketing Team');
        $this->command->info('  - Sales Team');
        $this->command->info('- Tech Startup Inc. (parent)');
        $this->command->info('  - Development Team');

        return [
            'main_company' => $mainCompany,
            'startup_company' => $startupCompany,
            'startup_dev_team' => $startupDevTeam,
        ];
    }

    /**
     * Tạo invitations mẫu
     */
    private function createSampleInvitations(array $organizations, array $users): void
    {
        // Tạo một số pending invitations cho ACME Corporation
        $pendingInvitations = [
            [
                'organization' => $organizations['main_company'],
                'inviter' => $users['admin'],
                'email' => '<EMAIL>',
                'role' => 'editor',
            ],
            [
                'organization' => $organizations['main_company'],
                'inviter' => $users['john_doe'],
                'email' => '<EMAIL>',
                'role' => 'member',
            ],
            [
                'organization' => $organizations['startup_company'],
                'inviter' => $users['jane_smith'],
                'email' => '<EMAIL>',
                'role' => 'editor',
            ],
            [
                'organization' => $organizations['startup_dev_team'],
                'inviter' => $users['mike_johnson'],
                'email' => '<EMAIL>',
                'role' => 'viewer',
            ],
        ];

        foreach ($pendingInvitations as $invitationData) {
            OrganizationInvitation::firstOrCreate(
                [
                    'organization_id' => $invitationData['organization']->id,
                    'email' => $invitationData['email'],
                ],
                [
                    'inviter_id' => $invitationData['inviter']->id,
                    'role' => $invitationData['role'],
                ]
            );
        }

        // Tạo một số expired invitations để test
        $expiredInvitations = [
            [
                'organization' => $organizations['main_company'],
                'inviter' => $users['admin'],
                'email' => '<EMAIL>',
                'role' => 'member',
            ],
            [
                'organization' => $organizations['startup_company'],
                'inviter' => $users['jane_smith'],
                'email' => '<EMAIL>',
                'role' => 'viewer',
            ],
        ];

        foreach ($expiredInvitations as $invitationData) {
            OrganizationInvitation::factory()
                ->expired()
                ->create([
                    'organization_id' => $invitationData['organization']->id,
                    'inviter_id' => $invitationData['inviter']->id,
                    'email' => $invitationData['email'],
                    'role' => $invitationData['role'],
                ]);
        }

        $this->command->info('Organization invitations seeded successfully!');
        $this->command->info('Created pending invitations: ' . count($pendingInvitations));
        $this->command->info('Created expired invitations: ' . count($expiredInvitations));
    }
}

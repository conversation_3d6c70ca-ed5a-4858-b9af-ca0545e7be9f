<?php

namespace Modules\User\Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Str;
use Modules\User\Models\User;
use Tests\TestCase;

class UserPublicControllerTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_get_user_profile_by_uuid()
    {
        $user = User::factory()->active()->verified()->create();

        $response = $this->getJson("/api/v1/users/public/{$user->uuid}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'User profile retrieved successfully'
            ])
            ->assertJsonStructure([
                'success',
                'data' => [
                    'uuid',
                    'username',
                    'full_name',
                    'first_name',
                    'last_name',
                    'avatar_url',
                    'gender',
                    'status',
                    'is_verified',
                    'newsletter_subscribed',
                    'location',
                    'created_at',
                    'updated_at'
                ],
                'message'
            ]);

        $this->assertEquals($user->uuid, $response->json('data.uuid'));
    }

    /** @test */
    public function it_returns_404_when_user_not_found_by_uuid()
    {
        $nonExistentUuid = (string) Str::uuid();

        $response = $this->getJson("/api/v1/users/public/{$nonExistentUuid}");

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'User not found'
            ]);
    }

    /** @test */
    public function it_can_check_if_user_exists_by_uuid()
    {
        $user = User::factory()->create();

        $response = $this->getJson("/api/v1/users/public/{$user->uuid}/exists");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'exists' => true,
                    'uuid' => $user->uuid
                ],
                'message' => 'User exists'
            ]);
    }

    /** @test */
    public function it_returns_false_when_checking_non_existent_user()
    {
        $nonExistentUuid = (string) Str::uuid();

        $response = $this->getJson("/api/v1/users/public/{$nonExistentUuid}/exists");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'exists' => false,
                    'uuid' => $nonExistentUuid
                ],
                'message' => 'User not found'
            ]);
    }

    /** @test */
    public function it_can_get_user_public_info_by_uuid()
    {
        $user = User::factory()->active()->verified()->create();

        $response = $this->getJson("/api/v1/users/public/{$user->uuid}/info");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'uuid' => $user->uuid,
                    'full_name' => $user->full_name,
                    'avatar_url' => $user->avatar_url,
                    'status' => $user->status->label(),
                    'is_verified' => $user->is_verified,
                ],
                'message' => 'User public information retrieved successfully'
            ])
            ->assertJsonStructure([
                'success',
                'data' => [
                    'uuid',
                    'full_name',
                    'avatar_url',
                    'status',
                    'is_verified',
                    'created_at'
                ],
                'message'
            ]);
    }

    /** @test */
    public function it_returns_404_when_getting_public_info_for_non_existent_user()
    {
        $nonExistentUuid = (string) Str::uuid();

        $response = $this->getJson("/api/v1/users/public/{$nonExistentUuid}/info");

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'User not found'
            ]);
    }

    /** @test */
    public function public_endpoints_do_not_expose_sensitive_data()
    {
        $user = User::factory()->create([
            'password' => 'secret-password',
            'remember_token' => 'secret-token',
        ]);

        $response = $this->getJson("/api/v1/users/public/{$user->uuid}");

        $response->assertStatus(200);
        
        $responseData = $response->json('data');
        
        // Ensure sensitive data is not exposed
        $this->assertArrayNotHasKey('id', $responseData);
        $this->assertArrayNotHasKey('password', $responseData);
        $this->assertArrayNotHasKey('remember_token', $responseData);
        $this->assertArrayNotHasKey('deleted_at', $responseData);
        
        // Ensure UUID is present instead of ID
        $this->assertArrayHasKey('uuid', $responseData);
        $this->assertEquals($user->uuid, $responseData['uuid']);
    }
}

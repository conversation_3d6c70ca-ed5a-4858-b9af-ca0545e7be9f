<?php

namespace Modules\ChatBot\Services;

use <PERSON><PERSON><PERSON>\ChatBot\Models\Conversation;
use <PERSON><PERSON><PERSON>\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Enums\MessageStatus;
use Illuminate\Support\Facades\Log;

class EmbeddingContextService
{
    /**
     * Build enhanced context with embedding results.
     */
    public function buildEnhancedContext(
        Conversation $conversation,
        array $contextData,
        array $relevantDocuments = []
    ): array {
        Log::info('Building enhanced context', [
            'conversation_id' => $conversation->id,
            'context_data_count' => count($contextData),
            'relevant_documents_count' => count($relevantDocuments),
        ]);

        $context = [];

        // Add system prompt with enhanced context
        $systemPrompt = $this->buildEnhancedSystemPrompt(
            $conversation->bot->system_prompt,
            $contextData
        );

        if ($systemPrompt) {
            $context[] = [
                'role' => 'system',
                'content' => $systemPrompt,
            ];
        }

        // Add conversation history
        $conversationHistory = $this->getConversationHistory($conversation);
        $context = array_merge($context, $conversationHistory);

        Log::info('Enhanced context built successfully', [
            'conversation_id' => $conversation->id,
            'total_context_messages' => count($context),
            'system_prompt_length' => strlen($systemPrompt),
        ]);

        return $context;
    }

    /**
     * Build enhanced system prompt with context data.
     */
    private function buildEnhancedSystemPrompt(
        ?string $originalSystemPrompt,
        array $contextData
    ): string {
        $prompt = $originalSystemPrompt ?? '';

        // Add context data section
        if (!empty($contextData)) {
            $prompt .= "\n\n## Relevant Context Information:\n";
            
            foreach ($contextData as $index => $data) {
                $prompt .= "\n### Context " . ($index + 1) . ":\n";
                
                if (is_array($data)) {
                    $prompt .= $this->formatContextData($data);
                } else {
                    $prompt .= $data;
                }
            }
        }

        // Add relevant documents section
        if (!empty($relevantDocuments)) {
            $prompt .= "\n\n## Relevant Documents:\n";
            
            foreach ($relevantDocuments as $index => $document) {
                $prompt .= "\n### Document " . ($index + 1) . ":\n";
                $prompt .= $this->formatDocument($document);
            }
        }

        // Add instructions for using context
        if (!empty($contextData) || !empty($relevantDocuments)) {
            $prompt .= "\n\n## Instructions:\n";
            $prompt .= "- Use the provided context information and documents to answer the user's question accurately.\n";
            $prompt .= "- If the context doesn't contain relevant information, clearly state that you don't have enough information.\n";
            $prompt .= "- Always cite or reference the relevant context when providing answers.\n";
            $prompt .= "- Maintain a helpful and professional tone.\n";
        }

        return $prompt;
    }

    /**
     * Format context data for inclusion in prompt.
     */
    private function formatContextData(array $data): string
    {
        $formatted = '';

        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $formatted .= "**{$key}**: " . json_encode($value, JSON_PRETTY_PRINT) . "\n";
            } else {
                $formatted .= "**{$key}**: {$value}\n";
            }
        }

        return $formatted;
    }

    /**
     * Format document for inclusion in prompt.
     */
    private function formatDocument(array $document): string
    {
        $formatted = '';

        // Document title
        if (isset($document['title'])) {
            $formatted .= "**Title**: {$document['title']}\n";
        }

        // Document content
        if (isset($document['content'])) {
            $formatted .= "**Content**: {$document['content']}\n";
        }

        // Document metadata
        if (isset($document['metadata'])) {
            $formatted .= "**Metadata**: " . json_encode($document['metadata'], JSON_PRETTY_PRINT) . "\n";
        }

        // Document source
        if (isset($document['source'])) {
            $formatted .= "**Source**: {$document['source']}\n";
        }

        // Document score/relevance
        if (isset($document['score'])) {
            $formatted .= "**Relevance Score**: {$document['score']}\n";
        }

        return $formatted;
    }

    /**
     * Get conversation history for context.
     */
    private function getConversationHistory(Conversation $conversation): array
    {
        $messages = $conversation->messages()
            ->whereIn('role', [MessageRole::USER, MessageRole::ASSISTANT, MessageRole::SYSTEM])
            ->where('status', MessageStatus::COMPLETED)
            ->orderBy('created_at')
            ->get();

        $history = [];

        foreach ($messages as $message) {
            $contextMessage = [
                'role' => $message->role->value,
                'content' => $message->content,
            ];

            if ($message->hasToolCalls()) {
                $contextMessage['tool_calls'] = $message->tool_calls;
            }

            $history[] = $contextMessage;
        }

        return $history;
    }

    /**
     * Extract key information from context data.
     */
    public function extractKeyInformation(array $contextData): array
    {
        $keyInfo = [
            'entities' => [],
            'topics' => [],
            'keywords' => [],
            'summary' => '',
        ];

        foreach ($contextData as $data) {
            if (is_array($data)) {
                // Extract entities
                if (isset($data['entities'])) {
                    $keyInfo['entities'] = array_merge($keyInfo['entities'], $data['entities']);
                }

                // Extract topics
                if (isset($data['topics'])) {
                    $keyInfo['topics'] = array_merge($keyInfo['topics'], $data['topics']);
                }

                // Extract keywords
                if (isset($data['keywords'])) {
                    $keyInfo['keywords'] = array_merge($keyInfo['keywords'], $data['keywords']);
                }

                // Extract summary
                if (isset($data['summary'])) {
                    $keyInfo['summary'] .= $data['summary'] . ' ';
                }
            }
        }

        // Remove duplicates and clean up
        $keyInfo['entities'] = array_unique($keyInfo['entities']);
        $keyInfo['topics'] = array_unique($keyInfo['topics']);
        $keyInfo['keywords'] = array_unique($keyInfo['keywords']);
        $keyInfo['summary'] = trim($keyInfo['summary']);

        return $keyInfo;
    }

    /**
     * Validate context data structure.
     */
    public function validateContextData(array $contextData): bool
    {
        if (empty($contextData)) {
            return false;
        }

        foreach ($contextData as $data) {
            if (!is_array($data) && !is_string($data)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Optimize context for token limits.
     */
    public function optimizeContextForTokens(array $context, int $maxTokens = 4000): array
    {
        // Simple optimization: truncate content if too long
        $optimized = [];
        $estimatedTokens = 0;

        foreach ($context as $message) {
            $messageTokens = $this->estimateTokens($message['content']);
            
            if ($estimatedTokens + $messageTokens > $maxTokens) {
                // Truncate content to fit within limits
                $remainingTokens = $maxTokens - $estimatedTokens;
                $truncatedContent = $this->truncateToTokens($message['content'], $remainingTokens);
                
                $message['content'] = $truncatedContent;
                $optimized[] = $message;
                break;
            }

            $optimized[] = $message;
            $estimatedTokens += $messageTokens;
        }

        return $optimized;
    }

    /**
     * Estimate token count for text (rough approximation).
     */
    private function estimateTokens(string $text): int
    {
        // Rough estimation: 1 token ≈ 4 characters
        return (int) ceil(strlen($text) / 4);
    }

    /**
     * Truncate text to approximate token count.
     */
    private function truncateToTokens(string $text, int $maxTokens): string
    {
        $maxChars = $maxTokens * 4;
        
        if (strlen($text) <= $maxChars) {
            return $text;
        }

        return substr($text, 0, $maxChars) . '...';
    }
}
